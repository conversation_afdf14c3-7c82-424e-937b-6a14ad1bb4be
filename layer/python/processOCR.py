from tqdm import tqdm # type: ignore
import json
import sys

from preProcessPDF import extract_page, get_pdf_reader_and_metadata
from model import generate, evaluate
from prompts import create_rubric_prompt, all_prompts
# from model import batchProcessing
import re
# from langchain_core.documents import Document

import schemas

subject_to_prompt_mapping = {
    "Paper A: Compulsory Indian Language": "paper_a_indian_language",
    "Paper B: Compulsory English": "paper_b_english",
    "General Studies Paper I": "gs_1",
    "General Studies Paper II": "gs_2",
    "General Studies Paper III": "gs_3",
    "General Studies Paper IV": "gs_4",
    "Essay": "essay",
    "Optional Subject": "optional_subject"
}

# def reorder_pages(pages, generic_metadata):
#     '''
#     Return type should be list of {page_content: "content", metadata: {generic + page number}}
#     '''
#     pattern = r'page_number:\s*\"?(\d+)\"?'

#     documentList = []

#     for page in pages:
#         # find the page number in the content
#         match = re.search(pattern, page)
#         metadata = generic_metadata.copy()
#         if match:
#             page_number = int(match.group(1))
#             metadata['page'] = page_number
#             documentList.append(Document(page_content= page, metadata=metadata))
#         else:
#             print(f'[Mehul] page number not found in the content')

#     # sort the documents by metadata page number
#     documentList.sort(key=lambda doc: doc.metadata.get('page', 0))
#     return documentList

def process_all_pages(pdf_path, file_type, subject):
    """
    Processes all pages in a PDF and returns an array of Document objects.
    """
    reader, metadata = get_pdf_reader_and_metadata(pdf_path)
    documents = []
    # inline_batch_ocr_request = []
    
    for page_number in tqdm(range(len(reader.pages)), desc=f"Processing {file_type} pages"):
        page_metadata = metadata.copy()
        page_metadata['page'] = page_number + 1
        
        b64_page = extract_page(reader, page_number)
        print(f"[Mehul] Subject in process_all_pages: {subject}")
        if file_type != "question_paper":
            prompt = all_prompts[subject_to_prompt_mapping[subject]]["answer_sheet_ocr"] if file_type == "answer_sheet" else all_prompts[subject_to_prompt_mapping[subject]]["rubric_ocr"]
        else:
            prompt = create_rubric_prompt # Default prompt to prevent crash
        document_page = generate(prompt, schemas.RubricOCR if file_type == "rubric" or file_type == "question_paper" else schemas.AnswerSheetOCR, b64_page)
        # inline_batch_ocr_request.append(batchGenerateRequest(b64_page, answer_sheet_prompt if file_type == "answer_sheet" else rubric_prompt if file_type == "rubric" else create_rubric_prompt, str(page_number)))
        documents.append(document_page)
    print(f"[PRINT] got documents: {documents[0]}", file=sys.stderr)
    # ocr_result = []
    # if (len(inline_batch_ocr_request) > 0):
    #     ocr_result, _ = batchProcessing(inline_batch_ocr_request, prompt=answer_sheet_prompt if file_type == "answer_sheet" else rubric_prompt if file_type == "rubric" else create_rubric_prompt, write_result=False)
        
    # return reorder_pages(ocr_result, metadata)# [{page_content: "content", metadata: {}}]
    return documents

def combine_and_evaluate(answer_sheet_data, rubric_data, subject, batch=False):
    
    # answer_sheet_data = ""
    # rubric_data = ""
    
    # with open(answer_sheet_path, 'r') as jsonl_file:
    #     for line in jsonl_file:
    #         try:
    #             data = json.loads(line)
    #             answer_sheet_data += data['page_content'] + "\n\n"
    #         except json.JSONDecodeError as e:
    #             print(f"Error parsing JSONL line in answer sheet: {str(e)}", file=sys.stderr)
    #         except Exception as e:
    #             print(f"Some other error please check: {str(e)}", file=sys.stderr)
    
    # with open(rubric_path, 'r') as jsonl_file:
    #     for line in jsonl_file:
    #         try:
    #             data = json.loads(line)
    #             rubric_data += data['page_content'] + "\n\n"
    #         except json.JSONDecodeError as e:
    #             print(f"Error parsing JSONL line in rubric: {str(e)}", file=sys.stderr)
    #         except Exception as e:
    #             print(f"Some other error please check: {str(e)}", file=sys.stderr)

    # with open('/tmp/answer_sheet_data.md', 'w') as md_file:
    #     md_file.write(answer_sheet_data)

    # with open('/tmp/rubric_data.md', 'w') as md_file:
    #     md_file.write(rubric_data)

    # print("Answer sheet data extracted successfully", file=sys.stderr)
    # print("Rubric data extracted successfully", file=sys.stderr)
    
    evaluation_prompt = all_prompts[subject_to_prompt_mapping[subject]]["evaluation"]
    if (not batch):
        try:
            evaluation_result = evaluate(evaluation_prompt, schemas.Evaluation, subject, rubric_data, answer_sheet_data)
            return evaluation_result
        except Exception as e:
            raise RuntimeError(f"Error during evaluation: {str(e)}")
    else:
        return batchEvaluateRequest(answer_sheet_data, rubric_data, evaluation_prompt)
    
# def create_rubric(created_rubric_path):
#     """
#     Creates a rubric for a question paper.
#     """
    
#     rubric_data = ""
#     with open(created_rubric_path, 'r') as jsonl_file:
#         for line in jsonl_file:
#             try:
#                 data = json.loads(line)
#                 rubric_data += data['page_content'] + "\n\n"
#             except json.JSONDecodeError as e:
#                 print(f"Error parsing JSONL line in created rubric: {str(e)}", file=sys.stderr)

#     with open('/tmp/created_rubric_data.md', 'w') as md_file:
#         md_file.write(rubric_data)

#     print("Created rubric data extracted successfully", file=sys.stderr)

#     return rubric_data

