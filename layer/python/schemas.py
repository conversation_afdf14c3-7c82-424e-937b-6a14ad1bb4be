from typing import List, Optional
from pydantic import BaseModel

# -------- RubricOCR --------
class RubricCriterion(BaseModel):
    name: str
    marks: float
    description: str

class RubricQuestion(BaseModel):
    number: str
    marks: float
    question_text: str
    model_answer: str
    marking_criteria: List[RubricCriterion]
    alternatives: Optional[List[str]]
    common_errors: Optional[List[str]]

class RubricOCR(BaseModel):
    page_number: int
    questions: List[RubricQuestion]

# -------- AnswerSheetOCR --------
class AnswerSheetAnswer(BaseModel):
    question_number: str
    answer_text: str
    confidence: float
    notes: Optional[str]

class AnswerSheetOCR(BaseModel):
    page_number: int
    answers: List[AnswerSheetAnswer]

# -------- Evaluation --------
class DetailedFeedback(BaseModel):
    overall_comment: Optional[str]
    structural_analysis: Optional[str]
    content_analysis: Optional[str]
    language_analysis: Optional[str]
    understanding_of_passage: Optional[str]
    accuracy_of_answers: Optional[str]
    clarity_of_expression: Optional[str]
    conciseness_and_length: Optional[str]
    coverage_of_main_points: Optional[str]
    clarity_and_coherence: Optional[str]
    title_relevance: Optional[str]
    fidelity_to_source: Optional[str]
    grammatical_correctness: Optional[str]
    idiomatic_usage: Optional[str]
    accuracy: Optional[str]

class EvaluationQuestion(BaseModel):
    number: str
    marks_awarded: float
    marks_possible: float
    detailed_feedback: DetailedFeedback

class Evaluation(BaseModel):
    total_marks_awarded: float
    maximum_possible_marks: float
    questions: List[EvaluationQuestion]
