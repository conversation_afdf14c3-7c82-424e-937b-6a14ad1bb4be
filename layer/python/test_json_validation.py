#!/usr/bin/env python3
"""
Test script for JSON validation utilities.

This script tests the JSON validation and cleanup functions with various
malformed JSON inputs to ensure robust error handling.
"""

import json
import sys
from json_validation_utils import (
    clean_json_response,
    validate_json_syntax,
    validate_schema_compliance,
    safe_parse_json_response,
    log_json_parsing_debug_info
)
from schemas import RubricOCR, AnswerSheetOCR


def test_clean_json_response():
    """Test the JSON cleaning function with various inputs."""
    print("Testing clean_json_response()...")
    
    test_cases = [
        # Valid JSON with markdown
        ('```json\n{"page_number": 1, "questions": []}\n```', '{"page_number": 1, "questions": []}'),
        
        # JSON with extra text
        ('Here is the JSON:\n{"page_number": 1, "questions": []}\nEnd of response', '{"page_number": 1, "questions": []}'),
        
        # J<PERSON><PERSON> with whitespace
        ('   \n  {"page_number": 1, "questions": []}  \n  ', '{"page_number": 1, "questions": []}'),
        
        # <PERSON>SO<PERSON> with code block but no language
        ('```\n{"page_number": 1, "questions": []}\n```', '{"page_number": 1, "questions": []}'),
    ]
    
    for i, (input_text, expected) in enumerate(test_cases):
        try:
            result = clean_json_response(input_text)
            if result == expected:
                print(f"  ✓ Test case {i+1} passed")
            else:
                print(f"  ✗ Test case {i+1} failed: expected {expected}, got {result}")
        except Exception as e:
            print(f"  ✗ Test case {i+1} failed with exception: {e}")


def test_validate_json_syntax():
    """Test JSON syntax validation."""
    print("\nTesting validate_json_syntax()...")
    
    test_cases = [
        ('{"valid": "json"}', True),
        ('{"invalid": json}', False),  # Missing quotes
        ('{"trailing": "comma",}', False),  # Trailing comma
        ('{"page_number": 1, "questions": []}', True),
        ('{page_number: 1}', False),  # Missing quotes on key
        ('[]', True),  # Valid empty array
        ('', False),  # Empty string
    ]
    
    for i, (json_str, should_be_valid) in enumerate(test_cases):
        is_valid, error = validate_json_syntax(json_str)
        if is_valid == should_be_valid:
            print(f"  ✓ Test case {i+1} passed")
        else:
            print(f"  ✗ Test case {i+1} failed: expected valid={should_be_valid}, got valid={is_valid}")
            if error:
                print(f"    Error: {error}")


def test_schema_validation():
    """Test schema validation against Pydantic models."""
    print("\nTesting validate_schema_compliance()...")
    
    # Valid RubricOCR data
    valid_rubric_data = {
        "page_number": 1,
        "questions": [
            {
                "number": "1",
                "marks": 10.0,
                "question_text": "Test question",
                "model_answer": "Test answer",
                "marking_criteria": [
                    {
                        "name": "Content",
                        "marks": 5.0,
                        "description": "Content quality"
                    }
                ],
                "alternatives": None,
                "common_errors": None
            }
        ]
    }
    
    # Invalid RubricOCR data (missing required field)
    invalid_rubric_data = {
        "page_number": 1,
        "questions": [
            {
                "number": "1",
                "marks": 10.0,
                # Missing question_text
                "model_answer": "Test answer",
                "marking_criteria": [],
                "alternatives": None,
                "common_errors": None
            }
        ]
    }
    
    # Valid AnswerSheetOCR data
    valid_answer_data = {
        "page_number": 1,
        "answers": [
            {
                "question_number": "1",
                "answer_text": "Student answer",
                "confidence": 0.95,
                "notes": None
            }
        ]
    }
    
    test_cases = [
        (valid_rubric_data, RubricOCR, True),
        (invalid_rubric_data, RubricOCR, False),
        (valid_answer_data, AnswerSheetOCR, True),
        (valid_rubric_data, AnswerSheetOCR, False),  # Wrong schema
    ]
    
    for i, (data, schema_class, should_be_valid) in enumerate(test_cases):
        is_valid, error = validate_schema_compliance(data, schema_class)
        if is_valid == should_be_valid:
            print(f"  ✓ Test case {i+1} passed")
        else:
            print(f"  ✗ Test case {i+1} failed: expected valid={should_be_valid}, got valid={is_valid}")
            if error:
                print(f"    Error: {error}")


def test_safe_parse_json_response():
    """Test the comprehensive JSON parsing function."""
    print("\nTesting safe_parse_json_response()...")
    
    # Valid response with markdown
    valid_response = '''```json
{
    "page_number": 1,
    "questions": [
        {
            "number": "1",
            "marks": 10.0,
            "question_text": "Test question",
            "model_answer": "Test answer",
            "marking_criteria": [
                {
                    "name": "Content",
                    "marks": 5.0,
                    "description": "Content quality"
                }
            ],
            "alternatives": null,
            "common_errors": null
        }
    ]
}
```'''
    
    # Response with trailing comma (should be fixed)
    response_with_trailing_comma = '''{
    "page_number": 1,
    "questions": [
        {
            "number": "1",
            "marks": 10.0,
            "question_text": "Test question",
            "model_answer": "Test answer",
            "marking_criteria": [
                {
                    "name": "Content",
                    "marks": 5.0,
                    "description": "Content quality",
                }
            ],
            "alternatives": null,
            "common_errors": null,
        }
    ],
}'''
    
    test_cases = [
        (valid_response, RubricOCR, True),
        (response_with_trailing_comma, RubricOCR, True),  # Should be fixed automatically
    ]
    
    for i, (response, schema_class, should_succeed) in enumerate(test_cases):
        try:
            result = safe_parse_json_response(response, schema_class)
            if should_succeed:
                print(f"  ✓ Test case {i+1} passed - parsed successfully")
                print(f"    Page number: {result.get('page_number')}")
                print(f"    Questions count: {len(result.get('questions', []))}")
            else:
                print(f"  ✗ Test case {i+1} failed - should have failed but succeeded")
        except Exception as e:
            if not should_succeed:
                print(f"  ✓ Test case {i+1} passed - failed as expected")
            else:
                print(f"  ✗ Test case {i+1} failed - should have succeeded but failed: {e}")


def main():
    """Run all tests."""
    print("Running JSON validation utility tests...\n")
    
    test_clean_json_response()
    test_validate_json_syntax()
    test_schema_validation()
    test_safe_parse_json_response()
    
    print("\nAll tests completed!")


if __name__ == "__main__":
    main()
