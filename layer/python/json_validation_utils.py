"""
JSON Validation Utilities for OCR and Evaluation Responses

This module provides utilities to validate and clean JSON responses from the AI model
to ensure they conform to the expected schemas and can be parsed correctly.
"""

import json
import re
from typing import Dict, Any, Optional, Union
import sys
from pydantic import BaseModel, ValidationError


def clean_json_response(raw_response: str) -> str:
    """
    Clean a raw API response to extract valid JSON.
    
    Args:
        raw_response: The raw response text from the API
        
    Returns:
        Cleaned JSON string
        
    Raises:
        ValueError: If no valid JSON can be extracted
    """
    # Remove leading/trailing whitespace
    cleaned = raw_response.strip()
    
    # Remove markdown code block formatting if present
    if cleaned.startswith("```json"):
        cleaned = cleaned[7:]
    elif cleaned.startswith("```"):
        cleaned = cleaned[3:]
        
    if cleaned.endswith("```"):
        cleaned = cleaned[:-3]
    
    # Remove any leading/trailing whitespace again
    cleaned = cleaned.strip()
    
    # Try to find JSON object boundaries if there's extra text
    json_start = cleaned.find('{')
    json_end = cleaned.rfind('}')
    
    if json_start != -1 and json_end != -1 and json_end > json_start:
        cleaned = cleaned[json_start:json_end + 1]
    
    return cleaned


def validate_json_syntax(json_str: str) -> tuple[bool, Optional[str]]:
    """
    Validate JSON syntax and return any error messages.
    
    Args:
        json_str: JSON string to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        json.loads(json_str)
        return True, None
    except json.JSONDecodeError as e:
        return False, str(e)


def validate_schema_compliance(data: Dict[str, Any], schema_class: type[BaseModel]) -> tuple[bool, Optional[str]]:
    """
    Validate that parsed JSON data complies with the expected Pydantic schema.
    
    Args:
        data: Parsed JSON data
        schema_class: Pydantic model class to validate against
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        schema_class(**data)
        return True, None
    except ValidationError as e:
        return False, str(e)


def safe_parse_json_response(
    raw_response: str, 
    schema_class: type[BaseModel],
    max_cleanup_attempts: int = 3
) -> Dict[str, Any]:
    """
    Safely parse and validate a JSON response with multiple cleanup attempts.
    
    Args:
        raw_response: Raw response text from API
        schema_class: Expected Pydantic schema class
        max_cleanup_attempts: Maximum number of cleanup attempts
        
    Returns:
        Parsed and validated JSON data
        
    Raises:
        ValueError: If JSON cannot be parsed or validated after all attempts
    """
    print(f"Attempting to parse JSON response (length: {len(raw_response)})", file=sys.stderr)
    
    for attempt in range(max_cleanup_attempts):
        try:
            # Clean the response
            cleaned_json = clean_json_response(raw_response)
            print(f"Cleanup attempt {attempt + 1}: {cleaned_json[:200]}...", file=sys.stderr)
            
            # Validate JSON syntax
            is_valid_json, json_error = validate_json_syntax(cleaned_json)
            if not is_valid_json:
                print(f"JSON syntax error on attempt {attempt + 1}: {json_error}", file=sys.stderr)
                
                # Try additional cleanup for common issues
                if attempt < max_cleanup_attempts - 1:
                    # Remove trailing commas
                    cleaned_json = re.sub(r',(\s*[}\]])', r'\1', cleaned_json)
                    # Fix unescaped quotes in strings (basic attempt)
                    cleaned_json = re.sub(r'(?<!\\)"(?=.*")', r'\\"', cleaned_json)
                    continue
                else:
                    raise ValueError(f"Invalid JSON syntax after {max_cleanup_attempts} attempts: {json_error}")
            
            # Parse JSON
            parsed_data = json.loads(cleaned_json)
            
            # Validate schema compliance
            is_valid_schema, schema_error = validate_schema_compliance(parsed_data, schema_class)
            if not is_valid_schema:
                print(f"Schema validation error on attempt {attempt + 1}: {schema_error}", file=sys.stderr)
                if attempt == max_cleanup_attempts - 1:
                    raise ValueError(f"Schema validation failed: {schema_error}")
                continue
            
            print(f"Successfully parsed and validated JSON on attempt {attempt + 1}", file=sys.stderr)
            return parsed_data
            
        except Exception as e:
            print(f"Parse attempt {attempt + 1} failed: {str(e)}", file=sys.stderr)
            if attempt == max_cleanup_attempts - 1:
                raise ValueError(f"Failed to parse JSON after {max_cleanup_attempts} attempts. Last error: {str(e)}")
    
    raise ValueError("Unexpected end of function")


def log_json_parsing_debug_info(raw_response: str, error: Exception) -> None:
    """
    Log detailed debug information for JSON parsing failures.
    
    Args:
        raw_response: The raw response that failed to parse
        error: The exception that occurred
    """
    print("=== JSON PARSING DEBUG INFO ===", file=sys.stderr)
    print(f"Error: {str(error)}", file=sys.stderr)
    print(f"Response length: {len(raw_response)}", file=sys.stderr)
    print(f"First 500 chars: {raw_response[:500]}", file=sys.stderr)
    print(f"Last 500 chars: {raw_response[-500:]}", file=sys.stderr)
    
    # Check for common issues
    if "```" in raw_response:
        print("WARNING: Response contains markdown code blocks", file=sys.stderr)
    
    if raw_response.count('{') != raw_response.count('}'):
        print(f"WARNING: Mismatched braces - {{ count: {raw_response.count('{')}, }} count: {raw_response.count('}')}", file=sys.stderr)
    
    if raw_response.count('[') != raw_response.count(']'):
        print(f"WARNING: Mismatched brackets - [ count: {raw_response.count('[')}, ] count: {raw_response.count(']')}", file=sys.stderr)
    
    # Look for trailing commas
    trailing_comma_pattern = r',(\s*[}\]])'
    if re.search(trailing_comma_pattern, raw_response):
        print("WARNING: Response contains trailing commas", file=sys.stderr)
    
    print("=== END DEBUG INFO ===", file=sys.stderr)
