AWSTemplateFormatVersion: "2010-09-09"
Transform: AWS::Serverless-2016-10-31
Description: >
  Aegis Grader service, split into a Lambda Layer (shared code & deps)
  and a small handler in src/

Globals:
  Function:
    Runtime: python3.13
    Architectures:
      - x86_64
    MemorySize: 512
    Timeout: 900
    EphemeralStorage:
      Size: 512

Resources:
  ##########################################################
  # 1) Build a layer from your `layer/` folder
  ##########################################################
  AegisGraderLayer:
    Type: AWS::Serverless::LayerVersion
    Metadata:
      BuildMethod: python3.13
      BuildArchitecture: x86_64
    Properties:
      LayerName: AegisGraderLayer
      Description: Shared code & dependencies for Aegis Grader
      ContentUri: layer # points at your layer/ folder
      CompatibleRuntimes:
        - python3.13
      CompatibleArchitectures:
        - x86_64

  ##########################################################
  # 2) Your main function, now using code from src/ + the layer
  ##########################################################
  AegisGraderFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: aegisgrader
      CodeUri: src/ # points at your src/ folder
      Handler: lambda_function.lambda_handler
      # these are inherited from Globals, but shown here for clarity:
      Runtime: python3.13

      # attach the layer you just defined
      Layers:
        - !Ref AegisGraderLayer

      Environment:
        Variables:
          GEMINI_API_KEY: AIzaSyBipPxHgYCgp6A07t1NCG9MyhxsbG9ZeMc
          MISTRAL_API_KEY: hSLn1rpt1wznVt8ff6po8seKtjWLh2rf
          MONGO_URI_TEST: >-
            mongodb+srv://Cluster09130:<EMAIL>/AegisScholarTestDb
          MONGO_URI_PROD: >-
            mongodb+srv://Cluster09130:<EMAIL>/AegisScholarDb
          S3_BUCKET: arn:aws:s3:::user-uploads-aegis-scholar

      PackageType: Zip

      # retry / error config
      EventInvokeConfig:
        MaximumEventAgeInSeconds: 21600
        MaximumRetryAttempts: 2

      # logging / SQS / S3 policies
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
              Resource: arn:aws:logs:ap-south-1:588738568415:*
            - Effect: Allow
              Action:
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource:
                - >-
                  arn:aws:logs:ap-south-1:588738568415:log-group:/aws/lambda/aegis_grader:*
            - Effect: Allow
              Action:
                - sqs:ReceiveMessage
                - sqs:DeleteMessage
                - sqs:GetQueueAttributes
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource: "*"
            - Effect: Allow
              Action:
                - s3:Get*
                - s3:List*
                - s3:Describe*
                - s3-object-lambda:Get*
                - s3-object-lambda:List*
              Resource: "*"

      # optional SAM features you had before
      RecursiveLoop: Terminate
      SnapStart:
        ApplyOn: None
      RuntimeManagementConfig:
        UpdateRuntimeOn: Auto

      Events:
        SQS1:
          Type: SQS
          Properties:
            Queue:
              Fn::GetAtt:
                - AegisGraderQueue
                - Arn
            BatchSize: 10

  ##########################################################
  # 3) Your SQS queue
  ##########################################################
  AegisGraderQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: AegisGraderQueue
      SqsManagedSseEnabled: true
      VisibilityTimeout: 960  # Must be >= Lambda timeout (900s) + buffer
